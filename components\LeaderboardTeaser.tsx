
import React, { useState, useEffect } from 'react';
import { Trophy, TrendingUp, BadgeCheck, Award, Star, Shield, Info, Crown, ChevronRight, Clock } from 'lucide-react';
import { Button } from '@/common/components/ui/button';
import { Link } from 'react-router-dom';
import { Badge } from '@/common/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/common/components/ui/tooltip';
import { cn } from '@/common/utils';

// Enhanced sample leaderboard data with badges and additional info (preview data)
const topTraders = [
  {
    rank: 1,
    name: "<PERSON>",
    profit: 14.23,
    isUp: true,
    change: "+2.1%",
    badges: [
      { name: "Top Trader", icon: Trophy, color: "text-amber-400", bgColor: "bg-amber-500/20" },
      { name: "Consistency King", icon: Award, color: "text-blue-400", bgColor: "bg-blue-500/20" }
    ],
    tradeCount: 42,
    winRate: 68,
    maxDrawdown: 3.2
  },
  {
    rank: 2,
    name: "<PERSON>",
    profit: 11.86,
    isUp: true,
    change: "+0.8%",
    badges: [
      { name: "Comeback Kid", icon: Star, color: "text-purple-400", bgColor: "bg-purple-500/20" }
    ],
    tradeCount: 37,
    winRate: 62,
    maxDrawdown: 4.1
  },
  {
    rank: 3,
    name: "David Chen",
    profit: 9.72,
    isUp: false,
    change: "-0.5%",
    badges: [
      { name: "Volume Monster", icon: Shield, color: "text-green-400", bgColor: "bg-green-500/20" }
    ],
    tradeCount: 56,
    winRate: 55,
    maxDrawdown: 5.3
  },
  {
    rank: 4,
    name: "Emma Rodriguez",
    profit: 8.45,
    isUp: true,
    change: "+1.3%",
    badges: [],
    tradeCount: 29,
    winRate: 59,
    maxDrawdown: 3.8
  },
  {
    rank: 5,
    name: "James Wilson",
    profit: 7.89,
    isUp: false,
    change: "-0.2%",
    badges: [],
    tradeCount: 33,
    winRate: 52,
    maxDrawdown: 4.5
  }
];

const LeaderboardTeaser = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [highlightedRow, setHighlightedRow] = useState<number | null>(null);

  // Simulated "last updated" time that updates every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Format the time for display
  const formattedTime = currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Dynamic background with more vibrant colors */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#0a1a3a] via-[#0f2a4a] to-[#0d1f35] z-0"></div>

      {/* Animated grid pattern with more visibility */}
      <div className="absolute inset-0 opacity-10 z-0">
        <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
          <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
            <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#4a88c7" strokeWidth="0.5" />
          </pattern>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>
      </div>

      {/* Enhanced glowing orbs with more vibrant colors */}
      <div className="absolute top-1/4 left-1/6 w-40 h-40 bg-blue-500/30 rounded-full filter blur-3xl animate-pulse-slow z-0"></div>
      <div className="absolute bottom-1/3 right-1/6 w-56 h-56 bg-cyan-400/20 rounded-full filter blur-3xl animate-pulse-slow z-0"></div>
      <div className="absolute top-2/3 left-1/3 w-32 h-32 bg-indigo-500/20 rounded-full filter blur-3xl animate-pulse-slow z-0"></div>

      {/* Animated lines in background */}
      <div className="absolute inset-0 opacity-20 overflow-hidden z-0">
        <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
          <path d="M0,50 Q25,30 50,50 T100,50" stroke="url(#gradient1)" strokeWidth="0.5" fill="none">
            <animate attributeName="d" dur="8s" repeatCount="indefinite" values="M0,50 Q25,30 50,50 T100,50;M0,50 Q25,70 50,50 T100,50;M0,50 Q25,30 50,50 T100,50" />
          </path>
          <path d="M0,30 Q25,50 50,30 T100,30" stroke="url(#gradient2)" strokeWidth="0.5" fill="none">
            <animate attributeName="d" dur="10s" repeatCount="indefinite" values="M0,30 Q25,50 50,30 T100,30;M0,30 Q25,10 50,30 T100,30;M0,30 Q25,50 50,30 T100,30" />
          </path>
          <path d="M0,70 Q25,50 50,70 T100,70" stroke="url(#gradient3)" strokeWidth="0.5" fill="none">
            <animate attributeName="d" dur="12s" repeatCount="indefinite" values="M0,70 Q25,50 50,70 T100,70;M0,70 Q25,90 50,70 T100,70;M0,70 Q25,50 50,70 T100,70" />
          </path>
          <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#0284c7" />
            <stop offset="100%" stopColor="#0ea5e9" />
          </linearGradient>
          <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#06b6d4" />
            <stop offset="100%" stopColor="#0891b2" />
          </linearGradient>
          <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#3b82f6" />
            <stop offset="100%" stopColor="#2563eb" />
          </linearGradient>
        </svg>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center max-w-2xl mx-auto mb-16" data-aos="fade-up">
          {/* More prominent preview badge */}
          <div className="inline-flex items-center px-5 py-2 bg-forex-primary/20 text-forex-primary font-medium rounded-full mb-6 border border-forex-primary/30 shadow-lg shadow-forex-primary/10 animate-fade-in">
            <Clock className="w-4 h-4 mr-2 animate-pulse" />
            <span>Leaderboard Preview</span>
          </div>

          {/* Improved heading with better spacing and alignment */}
          <h2 className="text-3xl md:text-5xl font-bold text-white mb-6 animate-fade-in leading-tight">
            Sneak Peek: <span className="bg-gradient-to-r from-forex-primary to-forex-accent bg-clip-text text-transparent">Future Champions</span>
          </h2>

          <p className="text-xl text-white/80 mb-8 animate-fade-in">
            A preview of how our real-time leaderboard will showcase top traders
          </p>
        </div>

        <div className="max-w-5xl mx-auto" data-aos="zoom-in" data-aos-delay="200">
          {/* Enhanced card with 3D effect and glass morphism - wider and more compact */}
          <div className="bg-[#0c1b31]/80 backdrop-blur-md rounded-xl border border-[#2a4d7d]/30 overflow-hidden shadow-xl transform transition-all hover:shadow-glow-primary">
            {/* Header with improved styling */}
            <div className="flex items-center justify-between bg-gradient-to-r from-[#0c2d5a] to-[#164279] px-6 py-3">
              <div className="flex items-center">
                <div className="flex items-center">
                  <Trophy className="w-5 h-5 text-amber-400 mr-2.5" />
                  <h3 className="font-bold text-white text-base">Daily Challenge Leaders</h3>
                </div>
                <Badge variant="outline" className="ml-3 text-xs bg-amber-500/20 text-amber-400 border-amber-500/30 px-2 py-0.5 flex items-center">
                  Sample Data
                </Badge>
              </div>
              <div className="text-sm text-white/80 flex items-center bg-[#0a1f3d]/50 px-3 py-1 rounded-full border border-[#2a4d7d]/30">
                <Clock className="w-3.5 h-3.5 mr-1.5 text-forex-primary" />
                <span>Last updated: {formattedTime}</span>
              </div>
            </div>

            {/* Make table responsive - only allow horizontal scroll on mobile */}
            <div className="overflow-x-auto md:overflow-x-hidden relative">
              <table className="w-full min-w-[650px] md:min-w-0">
                <thead>
                  <tr className="bg-[#0a1f3d] text-left border-b border-[#2a4d7d]/30">
                    <th className="px-3 md:px-5 py-3 text-xs font-semibold text-white/70 uppercase tracking-wider w-[60px] md:w-auto">Rank</th>
                    <th className="px-3 md:px-5 py-3 text-xs font-semibold text-white/70 uppercase tracking-wider">Name</th>
                    <th className="px-3 md:px-5 py-3 text-xs font-semibold text-white/70 uppercase tracking-wider text-right">Profit (%)</th>
                    <th className="px-3 md:px-5 py-3 text-xs font-semibold text-white/70 uppercase tracking-wider text-right hidden md:table-cell">Trades</th>
                    <th className="px-3 md:px-5 py-3 text-xs font-semibold text-white/70 uppercase tracking-wider text-right">24h Change</th>
                  </tr>
                </thead>
                <tbody>
                  {topTraders.map((trader, index) => (
                    <tr
                      key={index}
                      className={`transition-all duration-300 ${
                        highlightedRow === index ? "bg-[#1a3a5f]" :
                        index === 0
                          ? "bg-gradient-to-r from-amber-500/10 to-transparent"
                          : index === 1
                            ? "bg-gradient-to-r from-slate-400/10 to-transparent"
                            : index === 2
                              ? "bg-gradient-to-r from-amber-700/10 to-transparent"
                              : "bg-[#0c1b31]/40"
                      } hover:bg-[#1a3a5f] hover:scale-[1.01] hover:shadow-md border-b border-[#2a4d7d]/20`}
                      onMouseEnter={() => setHighlightedRow(index)}
                      onMouseLeave={() => setHighlightedRow(null)}
                    >
                      <td className="px-3 md:px-5 py-3">
                        <div className="flex items-center">
                          <span className={`w-7 h-7 md:w-8 md:h-8 rounded-full flex items-center justify-center text-xs font-bold mr-1 md:mr-2 ${
                            index === 0
                              ? "bg-gradient-to-br from-amber-500/30 to-amber-600/20 text-amber-400 border border-amber-500/50"
                              : index === 1
                                ? "bg-gradient-to-br from-slate-400/30 to-slate-500/20 text-slate-300 border border-slate-400/50"
                                : index === 2
                                  ? "bg-gradient-to-br from-amber-700/30 to-amber-800/20 text-amber-600 border border-amber-700/50"
                                  : "bg-[#1a3a5f] text-white/70 border border-[#2a4d7d]/30"
                          } transform transition-transform ${highlightedRow === index ? 'scale-110' : ''} shadow-md`}>
                            {index === 0 ? <Crown className="w-4 h-4" /> : trader.rank}
                          </span>
                          {index < 3 && (
                            <Trophy className={`w-3.5 h-3.5 ${
                              index === 0
                                ? "text-amber-400"
                                : index === 1
                                  ? "text-slate-300"
                                  : "text-amber-600"
                            } ${highlightedRow === index ? 'animate-bounce-soft' : ''}`} />
                          )}
                        </div>
                      </td>
                      <td className="px-3 md:px-5 py-3">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="flex items-center cursor-pointer">
                                <span className="font-medium text-white text-sm md:text-base">{trader.name}</span>
                                {trader.badges.length > 0 && (
                                  <div className="flex ml-1 md:ml-2">
                                    {trader.badges.slice(0, 2).map((badge, badgeIndex) => (
                                      <div key={badgeIndex} className={`${badge.bgColor} w-4 h-4 md:w-5 md:h-5 rounded-full flex items-center justify-center -ml-1 border border-forex-light/10 shadow-sm`}>
                                        <badge.icon className={`w-2.5 h-2.5 md:w-3 md:h-3 ${badge.color}`} />
                                      </div>
                                    ))}
                                    {trader.badges.length > 2 && (
                                      <div className="bg-forex-light/10 w-4 h-4 md:w-5 md:h-5 rounded-full flex items-center justify-center -ml-1 text-xs text-forex-light/70 shadow-sm">
                                        +{trader.badges.length - 2}
                                      </div>
                                    )}
                                  </div>
                                )}
                                {index === 0 && (
                                  <BadgeCheck className="w-3.5 h-3.5 md:w-4 md:h-4 text-forex-primary ml-1 md:ml-1.5" />
                                )}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent side="top" className="bg-[#0c1b31] border-forex-primary/20 text-forex-light p-3 max-w-xs shadow-xl">
                              <div className="space-y-2">
                                <p className="font-semibold text-white">{trader.name}</p>
                                <div className="grid grid-cols-2 gap-2 text-sm">
                                  <div>Win Rate: <span className="text-forex-profit font-medium">{trader.winRate}%</span></div>
                                  <div>Max DD: <span className="text-forex-loss font-medium">{trader.maxDrawdown}%</span></div>
                                </div>
                                {trader.badges.length > 0 && (
                                  <div className="pt-1.5">
                                    <p className="text-xs text-forex-light/70 mb-1.5">Achievements:</p>
                                    <div className="flex flex-wrap gap-1.5">
                                      {trader.badges.map((badge, badgeIndex) => (
                                        <div key={badgeIndex} className="flex items-center bg-[#1a3a5f] px-2 py-0.5 rounded-full text-xs">
                                          <badge.icon className={`w-3 h-3 ${badge.color} mr-1`} />
                                          <span>{badge.name}</span>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </td>
                      <td className="px-3 md:px-5 py-3 text-right font-bold text-forex-profit">
                        <div className={`inline-flex items-center justify-end ${highlightedRow === index ? 'animate-pulse' : ''}`}>
                          +{trader.profit}%
                        </div>
                      </td>
                      <td className="px-3 md:px-5 py-3 text-right hidden md:table-cell">
                        <span className="text-white/80 font-medium">{trader.tradeCount}</span>
                      </td>
                      <td className="px-3 md:px-5 py-3 text-right">
                        <div className={`flex items-center justify-end ${
                          trader.isUp ? "text-forex-profit" : "text-forex-loss"
                        }`}>
                          {trader.isUp ? (
                            <TrendingUp className={`w-3 h-3 md:w-3.5 md:h-3.5 mr-1 md:mr-1.5 ${highlightedRow === index ? 'animate-bounce-soft' : ''}`} />
                          ) : (
                            <TrendingUp className={`w-3 h-3 md:w-3.5 md:h-3.5 mr-1 md:mr-1.5 transform rotate-180 ${highlightedRow === index ? 'animate-bounce-soft' : ''}`} />
                          )}
                          <span className="font-medium">{trader.change}</span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="bg-[#0a1f3d] px-3 md:px-5 py-3 flex flex-col sm:flex-row justify-between items-center gap-2">
              <div className="flex items-center">
                <span className="text-xs md:text-sm text-white/70">
                  Showing top 5 of 237 traders
                </span>
              </div>
              <Link to="/leaderboard">
                <Button variant="ghost" size="sm" className="text-forex-primary hover:text-white hover:bg-forex-primary/20 group px-2 md:px-3 py-1.5 text-xs md:text-sm">
                  View Full Leaderboard
                  <ChevronRight className="ml-1 md:ml-1.5 h-3 w-3 md:h-4 md:w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </div>
          </div>

          {/* Featured badge section with improved design - more compact and compelling */}
          <div className="mt-8 bg-[#051326] rounded-xl border border-[#1a3a5f] overflow-hidden shadow-lg" data-aos="fade-up" data-aos-delay="300">
            <div className="flex items-center justify-between px-5 py-4">
              <div className="flex items-center">
                <div className="bg-amber-500/10 w-10 h-10 rounded-full flex items-center justify-center mr-4">
                  <Trophy className="w-5 h-5 text-amber-400" />
                </div>
                <div>
                  <h4 className="text-white font-bold text-base">Top Trader Badge</h4>
                  <p className="text-white/70 text-sm">Reach #1 on any leaderboard to earn this exclusive badge</p>
                </div>
              </div>
              <Button
                className="bg-amber-500 hover:bg-amber-600 text-white shadow-md px-4 py-2 text-sm font-medium whitespace-nowrap"
                onClick={() => document.getElementById('pricing')?.scrollIntoView({behavior: 'smooth'})}
              >
                Join Challenge to Earn
              </Button>
            </div>
          </div>


        </div>
      </div>
    </section>
  );
};

export default LeaderboardTeaser;
