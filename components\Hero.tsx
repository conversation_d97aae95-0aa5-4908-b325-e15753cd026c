import { Button } from "@/common/components/ui/button";
import { ChevronRight, TrendingUp, Shield, BadgeCheck, Trophy, Users, DollarSign, Zap, Target, Crown } from "lucide-react";
import Logo from "@/common/components/layout/Logo";
import { useIsMobile } from "@/common/hooks/use-mobile";
import { useAccount } from "wagmi";
import { useAuthStore } from "@/features/auth/stores/authStore";
import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";

/**
 * Hero component for TradeChampionX landing page
 *
 * Displays the main hero section for the crypto trading competition platform.
 * Features real-time stats, challenge highlights, and clear user journey paths
 * for traders, hosts, and spectators.
 *
 * Features:
 * - Real-time platform statistics
 * - Live challenge highlights
 * - Multiple user journey CTAs (Join Challenge, Become Host, Spectate)
 * - Animated trading competition visuals
 * - Mobile-optimized responsive design
 *
 * @component
 * @example
 * <Hero />
 */
const Hero = () => {
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const { isConnected } = useAccount();
  const { isAuthenticated } = useAuthStore();

  // Mock real-time stats (replace with actual API calls)
  const [stats, setStats] = useState({
    totalPrizePool: 125000,
    activeChallenges: 12,
    totalTraders: 2847,
    topROI: 156.7
  });

  // Animate stats on mount
  useEffect(() => {
    const interval = setInterval(() => {
      setStats(prev => ({
        ...prev,
        totalPrizePool: prev.totalPrizePool + Math.floor(Math.random() * 100),
        totalTraders: prev.totalTraders + Math.floor(Math.random() * 3)
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  /**
   * Handles different user journey actions
   */
  const handleJoinChallengeClick = () => {
    if (isConnected && isAuthenticated) {
      navigate('/dashboard');
    } else {
      document.getElementById('challenges')?.scrollIntoView({behavior: 'smooth'});
    }
  };

  const handleBecomeHostClick = () => {
    if (isConnected && isAuthenticated) {
      navigate('/host/apply');
    } else {
      // Scroll to host application section or show connect wallet modal
      document.getElementById('host-info')?.scrollIntoView({behavior: 'smooth'});
    }
  };

  const handleSpectateClick = () => {
    // Allow spectators to view challenges without wallet connection
    navigate('/challenges');
  };

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-tertiary pt-24 pb-16 md:pt-32 md:pb-24 overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-900/20 via-accent-purple/10 to-accent-cyan/10 pointer-events-none"></div>

      {/* Trading competition pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 border border-primary-500/30 rounded-full animate-pulse-slow"></div>
        <div className="absolute top-40 right-20 w-24 h-24 border border-accent-emerald/30 rounded-full animate-float"></div>
        <div className="absolute bottom-40 left-1/4 w-16 h-16 border border-accent-pink/30 rounded-full animate-bounce-subtle"></div>
        <div className="absolute bottom-20 right-1/3 w-20 h-20 border border-accent-orange/30 rounded-full animate-pulse-slow"></div>
      </div>

      {/* Floating trading icons */}
      <div className="absolute inset-0 pointer-events-none hidden lg:block">
        <div className="absolute top-32 left-20 animate-float">
          <Trophy className="w-8 h-8 text-accent-emerald/40" />
        </div>
        <div className="absolute top-60 right-32 animate-float" style={{animationDelay: '1s'}}>
          <Target className="w-6 h-6 text-primary-400/40" />
        </div>
        <div className="absolute bottom-60 left-32 animate-float" style={{animationDelay: '2s'}}>
          <Crown className="w-7 h-7 text-accent-orange/40" />
        </div>
        <div className="absolute bottom-32 right-20 animate-float" style={{animationDelay: '0.5s'}}>
          <Zap className="w-5 h-5 text-accent-purple/40" />
        </div>
      </div>

      {/* Animated particles - reduced quantity on mobile for better performance */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {Array.from({ length: isMobile ? 10 : 20 }).map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-forex-primary/30"
            style={{
              width: `${Math.random() * 10 + 5}px`,
              height: `${Math.random() * 10 + 5}px`,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              opacity: Math.random() * 0.5 + 0.3,
              animation: `float ${Math.random() * 20 + 10}s infinite ease-in-out`,
              animationDelay: `${Math.random() * 5}s`,
            }}
          />
        ))}
      </div>

      {/* Trading chart line animation - simplified on mobile for better performance */}
      <div className="absolute bottom-0 left-0 right-0 h-16 sm:h-24 opacity-20">
        <svg width="100%" height="100%" viewBox="0 0 1200 200" preserveAspectRatio="none">
          <path
            d="M0,100 C150,20 350,150 500,80 C650,10 800,120 1000,60 C1100,20 1200,80 1200,80 L1200,200 L0,200 Z"
            fill="url(#gradientChart)"
          >
            <animate
              attributeName="d"
              dur={isMobile ? "30s" : "20s"}
              repeatCount="indefinite"
              values="
                M0,100 C150,20 350,150 500,80 C650,10 800,120 1000,60 C1100,20 1200,80 1200,80 L1200,200 L0,200 Z;
                M0,80 C150,120 350,60 500,100 C650,140 800,60 1000,100 C1100,130 1200,100 1200,100 L1200,200 L0,200 Z;
                M0,100 C150,20 350,150 500,80 C650,10 800,120 1000,60 C1100,20 1200,80 1200,80 L1200,200 L0,200 Z"
            />
          </path>
          <defs>
            <linearGradient id="gradientChart" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#0284c7" />
              <stop offset="100%" stopColor="#0ea5e9" />
            </linearGradient>
          </defs>
        </svg>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Real-time Stats Bar */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="glass-card p-4 md:p-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8">
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-gradient mb-1">
                  ${stats.totalPrizePool.toLocaleString()}
                </div>
                <div className="text-sm text-text-tertiary">Total Prize Pool</div>
              </div>
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-accent-emerald mb-1">
                  {stats.activeChallenges}
                </div>
                <div className="text-sm text-text-tertiary">Live Challenges</div>
              </div>
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-accent-cyan mb-1">
                  {stats.totalTraders.toLocaleString()}
                </div>
                <div className="text-sm text-text-tertiary">Active Traders</div>
              </div>
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-accent-orange mb-1">
                  +{stats.topROI}%
                </div>
                <div className="text-sm text-text-tertiary">Top ROI Today</div>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto text-center">
          <div
            className="glass-card inline-block py-2 px-4 rounded-full mb-6 animate-fade-in border border-primary-500/30"
            data-aos="fade-down"
            data-aos-duration="800"
          >
            <span className="text-primary-400 text-sm sm:text-base font-medium flex items-center gap-2">
              <Trophy className="w-4 h-4" />
              Crypto Trading Competitions • Live Leaderboards • On-Chain Payouts
            </span>
          </div>

          <div className="mb-8" data-aos="zoom-in" data-aos-duration="1000">
            <Logo variant="white" size={isMobile ? "small" : "large"} showText={false} linkWrapper={false} />
          </div>

          <h1
            className="text-4xl xs:text-5xl md:text-6xl lg:text-7xl font-bold text-text-primary mb-6 animate-fade-in leading-tight"
            data-aos="fade-up"
            data-aos-duration="1000"
            data-aos-delay="100"
          >
            Trade. Compete.{" "}
            <span className="text-gradient">Dominate.</span>
          </h1>

          <p
            className="text-lg xs:text-xl md:text-2xl text-text-secondary mb-12 max-w-3xl mx-auto"
            data-aos="fade-up"
            data-aos-duration="1000"
            data-aos-delay="200"
          >
            Join influencer-hosted trading challenges, compete with real market data,
            and win USDC prizes paid directly to your wallet.
          </p>

          {/* Multiple User Journey CTAs */}
          <div
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            data-aos="fade-up"
            data-aos-duration="1000"
            data-aos-delay="300"
          >
            {/* Primary CTA - Join Challenge */}
            <Button
              size={isMobile ? "default" : "lg"}
              className="btn-primary text-white font-semibold text-sm sm:text-base px-6 sm:px-8 py-4 sm:py-6 w-full sm:w-auto shadow-lg hover-lift"
              onClick={handleJoinChallengeClick}
            >
              <Trophy className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
              Join Live Challenges
              <ChevronRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
            </Button>

            {/* Secondary CTA - Become Host */}
            <Button
              size={isMobile ? "default" : "lg"}
              variant="outline"
              className="border-primary-500/50 text-primary-400 hover:bg-primary-500/10 hover:border-primary-400 font-semibold text-sm sm:text-base px-6 sm:px-8 py-4 sm:py-6 w-full sm:w-auto hover-lift"
              onClick={handleBecomeHostClick}
            >
              <Crown className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
              Become a Host
            </Button>

            {/* Tertiary CTA - Spectate */}
            <Button
              size={isMobile ? "default" : "lg"}
              variant="ghost"
              className="text-text-secondary hover:text-text-primary hover:bg-glass-bg font-medium text-sm sm:text-base px-4 sm:px-6 py-4 sm:py-6 w-full sm:w-auto hover-lift"
              onClick={handleSpectateClick}
            >
              <Users className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
              Watch Live
            </Button>
          </div>



          {/* Key Features Grid - Trading Competition Focus */}
          <div
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-16"
            data-aos="fade-up"
            data-aos-duration="1000"
            data-aos-delay="400"
          >
            <div className="card-modern text-center hover-lift">
              <div className="w-12 h-12 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 glow-primary">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">Live Market Data</h3>
              <p className="text-sm text-text-tertiary">
                Paper trade with real-time market data. Server-authoritative fills ensure fair competition.
              </p>
            </div>

            <div className="card-modern text-center hover-lift">
              <div className="w-12 h-12 bg-gradient-secondary rounded-full flex items-center justify-center mx-auto mb-4 glow-success">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">On-Chain Escrow</h3>
              <p className="text-sm text-text-tertiary">
                Entry fees locked in smart contracts. Transparent prize pools with automatic payouts.
              </p>
            </div>

            <div className="card-modern text-center hover-lift">
              <div className="w-12 h-12 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-4">
                <DollarSign className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">USDC Prizes</h3>
              <p className="text-sm text-text-tertiary">
                Win real USDC paid directly to your wallet. No delays, no middlemen.
              </p>
            </div>
          </div>

          {/* Trust Indicators */}
          <div
            className="mt-16 flex flex-col sm:flex-row items-center justify-center gap-8 text-text-tertiary text-sm"
            data-aos="fade-up"
            data-aos-duration="1000"
            data-aos-delay="500"
          >
            <div className="flex items-center gap-2">
              <Shield className="w-4 h-4 text-accent-emerald" />
              <span>Base L2 Network</span>
            </div>
            <div className="flex items-center gap-2">
              <BadgeCheck className="w-4 h-4 text-primary-400" />
              <span>Licensed Market Data</span>
            </div>
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4 text-accent-cyan" />
              <span>No Wallet Required to Spectate</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
