import React from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>fo, <PERSON>geCheck, <PERSON><PERSON>, ArrowRight, ChevronRight, Star, Zap, Award, Clock } from 'lucide-react';
import { cn } from '@/common/utils';

const PrizeDistributionExplainer = () => {
  return (
    <section className="py-20 bg-gradient-to-b from-forex-dark to-forex-card relative overflow-hidden">
      {/* Background pattern and decorative elements - consistent with other sections */}
      <div className="absolute inset-0 bg-[url('/bg-pattern.svg')] opacity-10"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-forex-primary/5 to-forex-accent/5"></div>
      <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-forex-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-forex-accent/5 rounded-full blur-3xl"></div>

      <div className="max-w-7xl mx-auto px-6 relative z-10" data-aos="fade-up">
        <div className="text-center max-w-3xl mx-auto mb-8">
          <div className="inline-flex items-center px-4 py-1.5 bg-forex-primary/20 text-forex-primary font-medium rounded-full mb-4 shadow-sm backdrop-blur-sm border border-forex-primary/40">
            <Trophy className="w-4 h-4 mr-1.5 text-amber-400 animate-pulse-slow" />
            <span>DYNAMIC PRIZE DISTRIBUTION</span>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold heading-primary mb-4">
            Win Big With Our Community-First Model
          </h2>
          <p className="text-lg paragraph-bright mb-6">
            75% of all entry fees go directly back to our trading community
          </p>
        </div>

        {/* Modern, compact card with visual elements - matched with challenge card styling */}
        <div className="glass-card overflow-hidden transition-all duration-300 hover:shadow-2xl bg-forex-card/80 backdrop-blur-md border border-forex-border/20 p-10 relative rounded-xl shadow-xl">
          {/* Background decorative elements */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-forex-primary/10 to-transparent rounded-full blur-3xl -z-10"></div>
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-forex-accent/10 to-transparent rounded-full blur-3xl -z-10"></div>

        {/* Distribution visualization */}
        <div className="flex flex-col md:flex-row gap-12 items-center mb-10">
          <div className="w-60 h-auto flex-shrink-0 bg-forex-dark rounded-lg border border-forex-border/20 p-5 flex flex-col justify-center transform transition-all duration-300 hover:scale-105 hover:shadow-lg">
            {/* Community Share Header */}
            <div className="text-center mb-4">
              <div className="bg-gradient-to-r from-forex-primary to-forex-accent p-3 rounded-full inline-block mb-2 shadow-glow-primary group-hover:animate-pulse">
                <Trophy className="w-8 h-8 text-white" />
              </div>
              <p className="text-white text-sm font-medium">Community Share</p>
              <p className="bg-gradient-to-r from-forex-primary to-forex-accent bg-clip-text text-transparent text-4xl font-bold animate-pulse-slow">75%</p>
            </div>

            {/* Top Performers Bar - 60% */}
            <div className="mb-4 group">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-bold text-white flex items-center">
                  <Trophy className="w-4 h-4 mr-1.5 text-amber-400" />
                  Top Performers
                </span>
                <span className="text-sm font-bold text-forex-primary">60%</span>
              </div>
              <div className="w-full bg-forex-dark/80 rounded-full h-4 overflow-hidden">
                <div className="bg-gradient-to-r from-forex-primary to-forex-primary/70 h-full rounded-full group-hover:animate-pulse" style={{ width: '60%' }}></div>
              </div>
            </div>

            {/* Top 30% Bar - 15% */}
            <div className="mb-4 group">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-bold text-white flex items-center">
                  <Award className="w-4 h-4 mr-1.5 text-forex-accent" />
                  Top 30%
                </span>
                <span className="text-sm font-bold text-forex-accent">15%</span>
              </div>
              <div className="w-full bg-forex-dark/80 rounded-full h-4 overflow-hidden">
                <div className="bg-gradient-to-r from-forex-accent to-forex-accent/70 h-full rounded-full group-hover:animate-pulse" style={{ width: '15%' }}></div>
              </div>
            </div>

            {/* Platform Bar - 25% */}
            <div className="group">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-bold text-white flex items-center">
                  <PieChart className="w-4 h-4 mr-1.5 text-forex-light/70" />
                  Platform
                </span>
                <span className="text-sm font-bold text-forex-light/70">25%</span>
              </div>
              <div className="w-full bg-forex-dark/80 rounded-full h-4 overflow-hidden">
                <div className="bg-gradient-to-r from-forex-light/30 to-forex-light/10 h-full rounded-full group-hover:animate-pulse" style={{ width: '25%' }}></div>
              </div>
            </div>
          </div>

          <div className="flex-1 space-y-5">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-forex-dark p-4 rounded-lg border border-forex-border/20 hover:border-forex-primary/50 transition-all duration-300 hover:shadow-md hover:-translate-y-1 group">
                <div className="flex items-center justify-center mb-3">
                  <div className="w-6 h-6 rounded-full bg-forex-primary/20 flex items-center justify-center mr-2 group-hover:animate-pulse">
                    <Zap className="w-3.5 h-3.5 text-forex-primary" />
                  </div>
                  <h4 className="text-forex-primary text-sm font-bold uppercase">TOP PERFORMERS (60%)</h4>
                </div>
                <ul className="space-y-4">
                  <li className="text-xs text-forex-light/90 flex items-center bg-forex-card/40 p-2 rounded-md border border-forex-border/10 hover:bg-forex-card/60 transition-colors">
                    <span className="w-5 h-5 rounded-full bg-amber-500/30 flex items-center justify-center mr-2 text-amber-400 text-[10px] font-bold">1</span>
                    <span>Daily: <span className="text-white font-medium">50/30/20%</span> split</span>
                  </li>
                  <li className="text-xs text-forex-light/90 flex items-center bg-forex-card/40 p-2 rounded-md border border-forex-border/10 hover:bg-forex-card/60 transition-colors">
                    <span className="w-5 h-5 rounded-full bg-gray-400/30 flex items-center justify-center mr-2 text-gray-300 text-[10px] font-bold">2</span>
                    <span>Weekly: <span className="text-white font-medium">40/25/15%</span> + 20% 4th-10th</span>
                  </li>
                  <li className="text-xs text-forex-light/90 flex items-center bg-forex-card/40 p-2 rounded-md border border-forex-border/10 hover:bg-forex-card/60 transition-colors">
                    <span className="w-5 h-5 rounded-full bg-amber-700/30 flex items-center justify-center mr-2 text-amber-700 text-[10px] font-bold">3</span>
                    <span>Monthly: <span className="text-white font-medium">35/20/15%</span> + 20% 4th-10th</span>
                  </li>
                </ul>
              </div>

              <div className="bg-forex-dark p-4 rounded-lg border border-forex-border/20 hover:border-forex-accent/50 transition-all duration-300 hover:shadow-md hover:-translate-y-1 group">
                <div className="flex items-center justify-center mb-3">
                  <div className="w-6 h-6 rounded-full bg-forex-accent/20 flex items-center justify-center mr-2 group-hover:animate-pulse">
                    <Wallet className="w-3.5 h-3.5 text-forex-accent" />
                  </div>
                  <h4 className="text-forex-accent text-sm font-bold uppercase">WALLET CREDITS (15%)</h4>
                </div>
                <ul className="space-y-4">
                  <li className="text-sm text-forex-light/90 flex items-center bg-forex-card/40 p-2 rounded-md border border-forex-border/10 hover:bg-forex-card/60 transition-colors">
                    <BadgeCheck className="w-5 h-5 text-forex-accent mr-2" />
                    <span>Top 30% of performers</span>
                  </li>
                  <li className="text-sm text-forex-light/90 flex items-center bg-forex-card/40 p-2 rounded-md border border-forex-border/10 hover:bg-forex-card/60 transition-colors">
                    <BadgeCheck className="w-5 h-5 text-forex-accent mr-2" />
                    <span>Use for future entries</span>
                  </li>
                  <li className="text-sm text-forex-light/90 flex items-center bg-forex-card/40 p-2 rounded-md border border-forex-border/10 hover:bg-forex-card/60 transition-colors">
                    <BadgeCheck className="w-5 h-5 text-forex-accent mr-2" />
                    <span>30-day validity period</span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="bg-forex-dark p-5 rounded-lg border border-forex-border/20 hover:border-forex-border/40 transition-all duration-300 hover:shadow-md group">
              <div className="flex items-start">
                <div className="bg-gradient-to-r from-forex-primary to-forex-accent p-3 rounded-full mr-4 flex-shrink-0 group-hover:animate-pulse">
                  <Wallet className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h4 className="text-white text-base font-bold uppercase">HOW WALLET CREDITS WORK</h4>
                  <div className="h-0.5 w-20 bg-gradient-to-r from-forex-accent to-transparent rounded-full my-2"></div>
                  <p className="text-forex-light/80 text-sm">
                    Finish in the top 30% to earn credits automatically added to your wallet. Use them for future challenge entries or combine with crypto for partial payments. Credits expire after 30 days of inactivity.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom info section */}
        <div className="bg-gradient-to-r from-forex-primary/10 to-forex-accent/10 rounded-lg p-6 border border-forex-border/20 flex items-start mt-6 shadow-md hover:border-forex-border/40 transition-all duration-300 group">
          <div className="bg-gradient-to-r from-forex-primary to-forex-accent p-3 rounded-full mr-4 flex-shrink-0 group-hover:animate-pulse">
            <Info className="w-6 h-6 text-white" />
          </div>
          <div>
            <h4 className="text-white text-base font-bold mb-2">COMMUNITY-DRIVEN REWARDS</h4>
            <p className="text-forex-light/90 text-sm">
              Our dynamic prize pool grows with each participant. The more traders join, the bigger the rewards for everyone. This creates a truly community-driven reward system where everyone benefits from growth.
            </p>
          </div>
        </div>
      </div>
    </div>
    </section>
  );
};

export default PrizeDistributionExplainer;
