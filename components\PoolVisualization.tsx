import React from 'react';
import { DollarSign, ExternalLink, Shield, Users, Crown, Trophy } from 'lucide-react';
import { Button } from '@/common/components/ui/button';

interface PoolBreakdown {
  grossPool: number;
  platformFee: number;
  hostRake: number;
  winnerPool: number;
  prizeDistribution: {
    rank: number;
    percentage: number;
    amount: number;
  }[];
}

interface PoolVisualizationProps {
  challengeName: string;
  breakdown: PoolBreakdown;
  escrowAddress?: string;
  participants: number;
  entryFee: number;
  hostName?: string;
  className?: string;
}

export const PoolVisualization: React.FC<PoolVisualizationProps> = ({
  challengeName,
  breakdown,
  escrowAddress,
  participants,
  entryFee,
  hostName,
  className = ''
}) => {
  const openEtherscan = () => {
    if (escrowAddress) {
      window.open(`https://basescan.org/address/${escrowAddress}`, '_blank');
    }
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString()} USDC`;
  };

  const getBarSegments = () => {
    const total = breakdown.grossPool;
    return [
      {
        label: 'Platform Fee',
        amount: breakdown.platformFee,
        percentage: (breakdown.platformFee / total) * 100,
        color: 'bg-neutral-600'
      },
      {
        label: 'Host Rake',
        amount: breakdown.hostRake,
        percentage: (breakdown.hostRake / total) * 100,
        color: 'bg-accent-purple'
      },
      {
        label: 'Winner Pool',
        amount: breakdown.winnerPool,
        percentage: (breakdown.winnerPool / total) * 100,
        color: 'bg-gradient-to-r from-accent-emerald to-accent-cyan'
      }
    ];
  };

  return (
    <div className={`card-premium ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-bold text-text-primary flex items-center gap-2">
            <DollarSign className="w-5 h-5 text-accent-emerald" />
            Prize Pool Breakdown
          </h3>
          <p className="text-sm text-text-tertiary mt-1">{challengeName}</p>
        </div>
        
        {escrowAddress && (
          <Button
            onClick={openEtherscan}
            variant="outline"
            size="sm"
            className="border-primary-500/50 text-primary-400 hover:bg-primary-500/10"
          >
            <Shield className="w-4 h-4 mr-2" />
            View Escrow
            <ExternalLink className="w-4 h-4 ml-2" />
          </Button>
        )}
      </div>

      {/* Pool Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="text-center p-3 bg-glass-bg rounded-lg border border-glass-border">
          <div className="text-2xl font-bold text-text-primary">
            {participants}
          </div>
          <div className="text-sm text-text-tertiary">Participants</div>
        </div>
        
        <div className="text-center p-3 bg-glass-bg rounded-lg border border-glass-border">
          <div className="text-2xl font-bold text-accent-cyan">
            ${entryFee}
          </div>
          <div className="text-sm text-text-tertiary">Entry Fee</div>
        </div>
        
        <div className="text-center p-3 bg-glass-bg rounded-lg border border-glass-border">
          <div className="text-2xl font-bold text-accent-emerald">
            ${breakdown.grossPool.toLocaleString()}
          </div>
          <div className="text-sm text-text-tertiary">Gross Pool</div>
        </div>
        
        <div className="text-center p-3 bg-glass-bg rounded-lg border border-glass-border">
          <div className="text-2xl font-bold text-accent-orange">
            ${breakdown.winnerPool.toLocaleString()}
          </div>
          <div className="text-sm text-text-tertiary">Winner Pool</div>
        </div>
      </div>

      {/* Visual Breakdown Bar */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-text-secondary">Pool Distribution</span>
          <span className="text-sm text-text-tertiary">{formatCurrency(breakdown.grossPool)}</span>
        </div>
        
        <div className="w-full h-8 bg-bg-tertiary rounded-lg overflow-hidden flex">
          {getBarSegments().map((segment, index) => (
            <div
              key={index}
              className={`${segment.color} flex items-center justify-center text-xs font-medium text-white transition-all duration-500 hover:brightness-110`}
              style={{ width: `${segment.percentage}%` }}
              title={`${segment.label}: ${formatCurrency(segment.amount)} (${segment.percentage.toFixed(1)}%)`}
            >
              {segment.percentage > 15 && (
                <span>{segment.percentage.toFixed(0)}%</span>
              )}
            </div>
          ))}
        </div>
        
        {/* Legend */}
        <div className="flex flex-wrap gap-4 mt-3 text-sm">
          {getBarSegments().map((segment, index) => (
            <div key={index} className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-sm ${segment.color}`} />
              <span className="text-text-tertiary">
                {segment.label}: {formatCurrency(segment.amount)}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Prize Distribution */}
      <div className="space-y-4">
        <h4 className="text-lg font-semibold text-text-primary flex items-center gap-2">
          <Trophy className="w-5 h-5 text-accent-orange" />
          Prize Distribution
        </h4>
        
        <div className="space-y-2">
          {breakdown.prizeDistribution.map((prize, index) => (
            <div
              key={prize.rank}
              className="flex items-center justify-between p-3 bg-glass-bg rounded-lg border border-glass-border hover:border-primary-500/30 transition-colors"
            >
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-8 h-8 bg-gradient-primary rounded-full text-white font-bold text-sm">
                  {prize.rank}
                </div>
                <span className="text-text-secondary">
                  {prize.rank === 1 ? '1st Place' : 
                   prize.rank === 2 ? '2nd Place' : 
                   prize.rank === 3 ? '3rd Place' : 
                   `${prize.rank}th Place`}
                </span>
              </div>
              
              <div className="text-right">
                <div className="text-lg font-bold text-accent-emerald">
                  {formatCurrency(prize.amount)}
                </div>
                <div className="text-sm text-text-tertiary">
                  {prize.percentage}% of winner pool
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Host Info */}
      {hostName && (
        <div className="mt-6 pt-4 border-t border-neutral-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Crown className="w-4 h-4 text-accent-orange" />
              <span className="text-sm text-text-tertiary">Hosted by</span>
              <span className="text-sm font-medium text-text-primary">{hostName}</span>
            </div>
            {breakdown.hostRake > 0 && (
              <span className="text-sm text-text-tertiary">
                Host rake: {formatCurrency(breakdown.hostRake)}
              </span>
            )}
          </div>
        </div>
      )}

      {/* Trust Indicators */}
      <div className="mt-4 flex items-center justify-between text-xs text-text-tertiary">
        <div className="flex items-center gap-2">
          <Shield className="w-3 h-3 text-accent-emerald" />
          <span>Funds secured in smart contract</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-accent-emerald rounded-full animate-pulse" />
          <span>Live on Base L2</span>
        </div>
      </div>
    </div>
  );
};

export default PoolVisualization;
