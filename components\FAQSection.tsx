import React, { useState, useEffect, useRef } from "react";
import {
  Search,
  ChevronRight,
  X,
  MessageCircleQuestion,
  <PERSON>rkles,
  Check,
  ArrowUp,
  Info,
  Trophy,
  Coins,
  Wrench,
  ClipboardList,
  CreditCard,
  Users,
  Co<PERSON>,
  ThumbsUp,
  ThumbsDown,
  ArrowRight,
  ChevronDown
} from "lucide-react";
import { Input } from "@/common/components/ui/input";
import { cn } from "@/common/utils";
import { motion, AnimatePresence } from "framer-motion";

/**
 * Represents a single FAQ item
 * @interface FAQItem
 */
interface FAQItem {
  /** Unique identifier for the FAQ item */
  id: string;
  /** The question text */
  question: string;
  /** The answer text, can include markdown formatting */
  answer: string;
  /** The category this FAQ belongs to */
  category: string;
  /** Optional array of related question IDs */
  relatedQuestions?: string[];
}

/**
 * Represents a FAQ category
 * @interface Category
 */
interface Category {
  /** Unique identifier for the category */
  id: string;
  /** Display name of the category */
  name: string;
  /** Icon component to display with the category */
  icon: React.ReactNode;
  /** Gradient color for the category */
  color: string;
}

/**
 * FAQ Section component for the landing page
 *
 * Displays a comprehensive FAQ section with the following features:
 * - Categorized questions and answers
 * - Search functionality
 * - Related questions
 * - User feedback (helpful/not helpful)
 * - Copy link to specific questions
 * - Mobile-responsive design with category dropdown
 * - Back to top button
 *
 * @component
 * @example
 * <FAQSection />
 */
const FAQSection: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeCategory, setActiveCategory] = useState("general");
  const [activeQuestion, setActiveQuestion] = useState<string | null>(null);
  const [filteredFAQs, setFilteredFAQs] = useState<FAQItem[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchFocused, setSearchFocused] = useState(false);
  const [helpfulFeedback, setHelpfulFeedback] = useState<Record<string, boolean | null>>({});
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [showBackToTop, setShowBackToTop] = useState(false);
  const searchRef = useRef<HTMLInputElement>(null);

  // Function to handle feedback
  const handleFeedback = (id: string, isHelpful: boolean | null) => {
    setHelpfulFeedback(prev => ({
      ...prev,
      [id]: isHelpful
    }));
  };

  // Function to copy link to question
  const copyQuestionLink = (id: string) => {
    const url = `${window.location.origin}${window.location.pathname}#${id}`;
    navigator.clipboard.writeText(url).then(() => {
      setCopiedId(id);
      setTimeout(() => setCopiedId(null), 2000);
    });
  };

  // Initialize with the first category's questions
  useEffect(() => {
    if (!isSearching) {
      setFilteredFAQs(faqs.filter(faq => faq.category === categories.find(c => c.id === activeCategory)?.name));
    }
  }, [activeCategory, isSearching]);

  // Get active category color
  const getActiveCategoryColor = () => {
    return categories.find(c => c.id === activeCategory)?.color || "from-forex-primary to-forex-accent";
  };

  // Handle scroll for back to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Filter FAQs based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setIsSearching(false);
      setFilteredFAQs(faqs);
      return;
    }

    setIsSearching(true);
    const query = searchQuery.toLowerCase();
    const filtered = faqs.filter(
      (faq) =>
        faq.question.toLowerCase().includes(query) ||
        faq.answer.toLowerCase().includes(query)
    );
    setFilteredFAQs(filtered);
  }, [searchQuery]);

  // Clear search
  const clearSearch = () => {
    setSearchQuery("");
    setIsSearching(false);
    if (searchRef.current) {
      searchRef.current.focus();
    }
  };

  return (
    <section className="py-20 bg-gradient-to-b from-forex-dark to-[#0a1a2f] relative overflow-hidden">
      {/* Background pattern and decorative elements - consistent with other sections */}
      <div className="absolute inset-0 bg-[url('/bg-pattern.svg')] opacity-10"></div>
      <div className="absolute inset-0 bg-gradient-to-br from-forex-primary/5 to-forex-accent/5"></div>
      <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-forex-primary/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-forex-accent/5 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Back to Top Button */}
        <AnimatePresence>
          {showBackToTop && (
            <motion.button
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              transition={{ duration: 0.2 }}
              className="fixed bottom-6 right-6 z-50 bg-forex-primary text-white p-3 rounded-full shadow-lg hover:bg-forex-hover transition-all"
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
              aria-label="Back to top"
            >
              <ArrowUp className="w-5 h-5" />
            </motion.button>
          )}
        </AnimatePresence>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center max-w-3xl mx-auto mb-10"
        >
          <motion.span
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.4 }}
            className="inline-flex items-center px-5 py-2 bg-forex-primary/20 text-forex-primary font-semibold rounded-full mb-4 shadow-md backdrop-blur-sm border border-forex-primary/50"
          >
            <Sparkles className="w-4 h-4 mr-2 text-forex-primary" />
            <span className="bg-gradient-to-r from-forex-primary to-forex-accent bg-clip-text text-transparent">Knowledge Center</span>
          </motion.span>
          <h2 className="text-3xl md:text-5xl font-bold heading-primary mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-sm md:text-lg paragraph-bright mb-10 max-w-2xl mx-auto">
            Everything you need to know about our trading challenges and how to get started
          </p>

          {/* Search Bar */}
          <div className="relative max-w-md mx-auto">
            <div className="flex">
              <div className="relative flex-grow">
                <div className={cn(
                  "absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none transition-all",
                  searchFocused ? "text-forex-primary" : "text-forex-light/50"
                )}>
                  <Search className="h-4 w-4" />
                </div>
                <Input
                  ref={searchRef}
                  type="search"
                  placeholder="Search for answers..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => setSearchFocused(true)}
                  onBlur={() => setSearchFocused(false)}
                  className={cn(
                    "pl-12 py-3 bg-forex-card/60 backdrop-blur-sm border-forex-border/40 text-white focus:border-forex-primary focus:ring-forex-primary/20 rounded-xl text-sm shadow-lg transition-all",
                    searchFocused ? "border-forex-primary ring-2 ring-forex-primary/20" : ""
                  )}
                />
                {searchQuery && (
                  <motion.button
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    onClick={clearSearch}
                    className="absolute inset-y-0 right-0 flex items-center pr-4 text-forex-light/70 hover:text-forex-primary transition-colors"
                  >
                    <X className="h-4 w-4" />
                  </motion.button>
                )}
              </div>
            </div>
          </div>
        </motion.div>

        {/* FAQ Content with Sidebar */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="max-w-6xl mx-auto bg-forex-card/30 backdrop-blur-md rounded-2xl overflow-hidden border border-forex-border/30 shadow-xl glass-dark"
        >
          <div className="flex flex-col md:flex-row">
            {/* Sidebar Navigation */}
            <div className="md:w-72 bg-gradient-to-b from-forex-card/60 to-forex-card/40 border-b md:border-b-0 md:border-r border-forex-border/20">
              {/* Mobile Category Selector */}
              <div className="md:hidden p-4">
                <div className="relative">
                  <button
                    className="w-full flex items-center justify-between bg-forex-card/60 text-white px-4 py-3 rounded-xl border border-forex-border/30"
                    onClick={() => {
                      const dropdown = document.getElementById('mobile-category-dropdown');
                      if (dropdown) {
                        dropdown.classList.toggle('hidden');
                      }
                    }}
                  >
                    <div className="flex items-center">
                      <span className="mr-3 text-forex-primary">
                        {categories.find(c => c.id === activeCategory)?.icon}
                      </span>
                      <span className="text-forex-dark font-medium">{categories.find(c => c.id === activeCategory)?.name}</span>
                    </div>
                    <ChevronDown className="w-4 h-4 text-forex-dark" />
                  </button>

                  <div id="mobile-category-dropdown" className="absolute left-0 right-0 mt-2 bg-forex-card/95 backdrop-blur-md rounded-xl border border-forex-border/30 shadow-xl z-20 hidden">
                    <ul className="py-2">
                      {categories.map((category) => (
                        <li key={category.id} className="px-2">
                          <button
                            onClick={() => {
                              setActiveCategory(category.id);
                              setIsSearching(false);
                              setSearchQuery("");
                              const dropdown = document.getElementById('mobile-category-dropdown');
                              if (dropdown) {
                                dropdown.classList.add('hidden');
                              }
                            }}
                            className={cn(
                              "w-full text-left px-4 py-3 rounded-xl flex items-center text-sm transition-all",
                              activeCategory === category.id && !isSearching
                                ? `bg-gradient-to-r ${category.color} text-white`
                                : "text-white font-medium hover:bg-forex-card/60 hover:text-white"
                            )}
                          >
                            <span className="mr-3 text-white">{category.icon}</span>
                            <span className="text-shadow-sm">{category.name}</span>
                          </button>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              {/* Desktop Sidebar */}
              <nav className="hidden md:block p-4">
                <div className="mb-4 px-2">
                  <h3 className="text-xs uppercase tracking-wider text-white font-semibold">Categories</h3>
                </div>
                <ul className="space-y-1.5">
                  {categories.map((category) => (
                    <motion.li
                      key={category.id}
                      whileHover={{ x: 4 }}
                      transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    >
                      <button
                        onClick={() => {
                          setActiveCategory(category.id);
                          setIsSearching(false);
                          setSearchQuery("");
                        }}
                        className={cn(
                          "w-full text-left px-4 py-3 rounded-xl flex items-center text-sm transition-all",
                          activeCategory === category.id && !isSearching
                            ? `bg-gradient-to-r ${category.color} text-white shadow-md`
                            : "text-white font-medium hover:bg-forex-card/60 hover:text-white"
                        )}
                      >
                        <span className="mr-3 text-white">{category.icon}</span>
                        <span className="text-shadow-sm">{category.name}</span>
                        {activeCategory === category.id && !isSearching && (
                          <ArrowRight className="w-3.5 h-3.5 ml-auto" />
                        )}
                      </button>
                    </motion.li>
                  ))}
                </ul>
              </nav>
            </div>

            {/* FAQ Content */}
            <div className="flex-1 p-6 md:p-8 max-h-[70vh] overflow-y-auto scrollbar-hide">

              <AnimatePresence mode="wait">
                {isSearching ? (
                  <motion.div
                    key="search-results"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="mb-6 pb-4 border-b border-forex-border/20"
                  >
                    <h3 className="text-lg font-medium text-white flex items-center">
                      <Search className="w-4 h-4 mr-2 text-forex-primary" />
                      Search Results {filteredFAQs.length > 0 ? `(${filteredFAQs.length})` : ''}
                    </h3>
                  </motion.div>
                ) : (
                  <motion.div
                    key="category-header"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="mb-6 pb-4 border-b border-forex-border/20"
                  >
                    <h3 className="text-xl font-medium text-white flex items-center">
                      <span className="mr-3">{categories.find(c => c.id === activeCategory)?.icon}</span>
                      {categories.find(c => c.id === activeCategory)?.name}
                    </h3>
                  </motion.div>
                )}
              </AnimatePresence>

              {filteredFAQs.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center py-12 px-6 bg-forex-card/20 rounded-xl border border-forex-border/10"
                >
                  <p className="text-white mb-3">No results found for "{searchQuery}"</p>
                  <button
                    onClick={clearSearch}
                    className="px-4 py-2 bg-forex-primary/20 hover:bg-forex-primary/30 text-forex-primary rounded-lg transition-colors text-sm"
                  >
                    Clear search
                  </button>
                </motion.div>
              ) : (
                <div className="space-y-4">
                  <AnimatePresence initial={false}>
                    {filteredFAQs.map((faq, index) => (
                      <motion.div
                        id={faq.id}
                        key={faq.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05, duration: 0.3 }}
                        className={cn(
                          "bg-forex-card/40 backdrop-blur-sm rounded-xl border overflow-hidden transition-all shadow-sm hover:shadow-md",
                          activeQuestion === faq.id
                            ? "border-forex-primary/40 shadow-md"
                            : "border-forex-border/20 hover:border-forex-border/40"
                        )}
                      >
                        <button
                          onClick={() => setActiveQuestion(activeQuestion === faq.id ? null : faq.id)}
                          className="w-full px-5 py-4 flex justify-between items-center text-left focus:outline-none"
                        >
                          <h4 className="text-sm font-medium text-on-dark force-visible-text pr-4">
                            {highlightText(faq.question, searchQuery)}
                          </h4>
                          <motion.div
                            animate={{ rotate: activeQuestion === faq.id ? 90 : 0 }}
                            transition={{ duration: 0.2 }}
                            className={cn(
                              "flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-full",
                              activeQuestion === faq.id
                                ? `bg-gradient-to-r ${getActiveCategoryColor()}`
                                : "bg-forex-card/60"
                            )}
                          >
                            <ChevronRight className="h-3.5 w-3.5 text-white" />
                          </motion.div>
                        </button>

                        <AnimatePresence>
                          {activeQuestion === faq.id && (
                            <motion.div
                              initial={{ height: 0, opacity: 0 }}
                              animate={{ height: "auto", opacity: 1 }}
                              exit={{ height: 0, opacity: 0 }}
                              transition={{ duration: 0.3, ease: "easeInOut" }}
                              className="overflow-hidden"
                            >
                              <div className="px-5 py-4 border-t border-forex-border/10 bg-gradient-to-b from-forex-card/60 to-forex-card/20">
                                <p className="text-sm text-on-dark leading-relaxed force-visible-text">
                                  {highlightText(faq.answer, searchQuery)}
                                </p>

                                {/* Related Questions */}
                                {faq.relatedQuestions && faq.relatedQuestions.length > 0 && (
                                  <div className="mt-4 pt-3 border-t border-forex-border/10">
                                    <h5 className="text-xs font-medium text-on-dark force-visible-text mb-2">Related Questions</h5>
                                    <div className="space-y-1.5">
                                      {faq.relatedQuestions.map(relatedId => {
                                        const relatedFaq = faqs.find(f => f.id === relatedId);
                                        return relatedFaq ? (
                                          <button
                                            key={relatedId}
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              setActiveQuestion(relatedId);
                                              // Scroll to the related question
                                              setTimeout(() => {
                                                document.getElementById(relatedId)?.scrollIntoView({
                                                  behavior: 'smooth',
                                                  block: 'center'
                                                });
                                              }, 100);
                                            }}
                                            className="text-xs text-forex-primary hover:underline flex items-center"
                                          >
                                            <MessageCircleQuestion className="w-3 h-3 mr-1.5 flex-shrink-0" />
                                            <span>{relatedFaq.question}</span>
                                          </button>
                                        ) : null;
                                      })}
                                    </div>
                                  </div>
                                )}

                                {/* Feedback and Tools */}
                                <div className="mt-4 pt-3 border-t border-forex-border/10 flex flex-wrap items-center justify-between gap-2">
                                  {/* Category (when searching) */}
                                  {isSearching && (
                                    <div className="text-xs text-on-dark force-visible-text flex items-center">
                                      <span className="mr-1">Category:</span>
                                      <button
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          setActiveCategory(categories.find(c => c.name === faq.category)?.id || "general");
                                          setIsSearching(false);
                                          setSearchQuery("");
                                        }}
                                        className="text-forex-primary hover:underline"
                                      >
                                        {faq.category}
                                      </button>
                                    </div>
                                  )}

                                  {/* Feedback */}
                                  <div className="flex items-center space-x-4">
                                    {/* Copy Link */}
                                    <button
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        copyQuestionLink(faq.id);
                                      }}
                                      className="text-xs text-on-dark force-visible-text hover:text-forex-primary flex items-center"
                                      title="Copy link to this question"
                                    >
                                      {copiedId === faq.id ? (
                                        <span className="flex items-center">
                                          <Check className="w-3 h-3 mr-1" />
                                          Copied!
                                        </span>
                                      ) : (
                                        <span className="flex items-center">
                                          <Copy className="w-3 h-3 mr-1" />
                                          Copy Link
                                        </span>
                                      )}
                                    </button>

                                    {/* Was this helpful */}
                                    <div className="flex items-center space-x-2">
                                      <span className="text-xs text-on-dark force-visible-text">Helpful?</span>
                                      <button
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleFeedback(faq.id, true);
                                        }}
                                        className={`text-xs p-1 rounded-full ${
                                          helpfulFeedback[faq.id] === true
                                            ? "bg-green-500/20 text-green-500"
                                            : "text-on-dark force-visible-text hover:text-forex-primary"
                                        }`}
                                        title="Yes, this was helpful"
                                      >
                                        <ThumbsUp className="w-3 h-3" />
                                      </button>
                                      <button
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleFeedback(faq.id, false);
                                        }}
                                        className={`text-xs p-1 rounded-full ${
                                          helpfulFeedback[faq.id] === false
                                            ? "bg-red-500/20 text-red-500"
                                            : "text-on-dark force-visible-text hover:text-forex-primary"
                                        }`}
                                        title="No, this wasn't helpful"
                                      >
                                        <ThumbsDown className="w-3 h-3" />
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              )}

              {/* Contact Support Section */}
              <div className="mt-10 pt-6 border-t border-forex-border/20">
                <div className="bg-gradient-to-r from-forex-primary/10 to-forex-accent/10 rounded-xl p-8 border border-forex-border/20 text-center glass-dark">
                  <h3 className="text-xl font-bold text-on-dark force-visible-text mb-3">Still have questions?</h3>
                  <p className="text-on-dark force-visible-text mb-6 max-w-xl mx-auto">
                    Our support team is ready to help with any questions about our trading challenges that aren't covered here.
                  </p>
                  <a
                    href="#"
                    className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-forex-primary to-forex-accent text-white rounded-lg hover:opacity-90 transition-all duration-300 font-medium shadow-lg hover:shadow-forex-primary/20"
                  >
                    <MessageCircleQuestion className="w-5 h-5 mr-2" />
                    Contact Support
                  </a>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Custom scrollbar styles added via CSS */}
    </section>
  );
};

// Helper function to highlight search terms
const highlightText = (text: string, query: string) => {
  if (!query.trim()) return text;

  const parts = text.split(new RegExp(`(${query})`, 'gi'));
  return (
    <>
      {parts.map((part, i) =>
        part.toLowerCase() === query.toLowerCase() ? (
          <span key={i} className="bg-forex-primary/20 text-forex-primary px-1 rounded">
            {part}
          </span>
        ) : (
          part
        )
      )}
    </>
  );
};

// Categories
const categories: Category[] = [
  { id: "general", name: "General Information", icon: <Info className="w-5 h-5" />, color: "from-blue-500 to-blue-600" },
  { id: "challenges", name: "Trading Challenges", icon: <Trophy className="w-5 h-5" />, color: "from-amber-500 to-orange-600" },
  { id: "prizes", name: "Prizes & Rewards", icon: <Coins className="w-5 h-5" />, color: "from-green-500 to-emerald-600" },
  { id: "technical", name: "Technical", icon: <Wrench className="w-5 h-5" />, color: "from-purple-500 to-indigo-600" },
  { id: "rules", name: "Rules & Requirements", icon: <ClipboardList className="w-5 h-5" />, color: "from-red-500 to-rose-600" },
  { id: "payments", name: "Payments & Accounts", icon: <CreditCard className="w-5 h-5" />, color: "from-teal-500 to-cyan-600" },
  { id: "community", name: "Community", icon: <Users className="w-5 h-5" />, color: "from-forex-primary to-forex-accent" }
];

// FAQ data
const faqs: FAQItem[] = [
  {
    id: "faq-1",
    question: "What is TradeChampionX?",
    answer: "TradeChampionX is a competitive trading platform where traders participate in time-based challenges to demonstrate their skills and compete for prize pools. Unlike traditional prop firms that profit from trader failures, our community-focused model distributes 75% of all entry fees directly back to participants. We offer daily, weekly, and monthly challenges with transparent rules, fair competition, and guaranteed payouts to winners.",
    category: "General Information",
    relatedQuestions: ["faq-2", "faq-3", "faq-4"]
  },
  {
    id: "faq-2",
    question: "How do I get started?",
    answer: "Getting started with TradeChampionX is simple:\n\n• Step 1: Create a free account\n   - Sign up on our platform with your email\n   - Verify your account\n\n• Step 2: Choose your challenge\n   - Browse available challenges\n   - Select one matching your trading style and timeframe\n\n• Step 3: Pay the entry fee\n   - Use cryptocurrency or wallet credits\n   - Secure payment processing\n\n• Step 4: Connect your cTrader account\n   - Link through our secure OAuth process\n   - No sharing of passwords required\n\n• Step 5: Start trading!\n   - Our system automatically tracks your performance\n   - No manual reporting needed",
    category: "General Information",
    relatedQuestions: ["faq-11", "faq-17", "faq-4"]
  },
  {
    id: "faq-3",
    question: "What makes TradeChampionX different from prop firms?",
    answer: "TradeChampionX fundamentally differs from traditional prop firms in several key ways:\n\n• Community-Focused Model\n   - We distribute 75% of all entry fees back to the community\n   - Traditional firms profit when traders fail\n\n• Transparent Rules\n   - No hidden disqualification clauses\n   - No restrictive trading windows\n   - No manipulated spreads\n\n• Guaranteed Payouts\n   - No withdrawal restrictions or delays\n   - No minimum withdrawal amounts\n\n• Fair Competition\n   - Traders compete against each other, not against the house\n   - Success of one trader doesn't negatively impact the platform",
    category: "General Information",
    relatedQuestions: ["faq-7", "faq-15", "faq-10"]
  },
  {
    id: "faq-4",
    question: "What types of challenges do you offer?",
    answer: "We offer three distinct challenge types to accommodate different trading styles and time commitments:\n\n• Daily Challenges\n   - Perfect for day traders seeking quick results\n   - Lower entry fees and faster payouts\n   - 24-hour competition window\n\n• Weekly Challenges\n   - Ideal for swing traders\n   - Balanced mix of time commitment and reward potential\n   - 7-day competition window\n\n• Monthly Challenges\n   - Designed for position traders\n   - Largest prize pools and most flexible trading conditions\n   - 30-day competition window\n\nEach challenge type has specific rules and requirements tailored to its timeframe.",
    category: "Trading Challenges"
  },
  {
    id: "faq-5",
    question: "How long do challenges last?",
    answer: "Our challenges have clearly defined timeframes: Daily Challenges run for exactly 24 hours from the moment you enter, giving you a full trading day to demonstrate your skills. Weekly Challenges last for 7 consecutive days, allowing for multiple trading sessions and different market conditions. Monthly Challenges extend for 30 days, providing ample time to implement longer-term strategies and recover from any temporary drawdowns. All challenge timers are visible on your dashboard for easy tracking.",
    category: "Trading Challenges"
  },
  {
    id: "faq-6",
    question: "Can I participate in multiple challenges simultaneously?",
    answer: "Yes, you can participate in multiple challenges simultaneously without any restrictions. Each challenge is tracked separately in our system, allowing you to test different strategies across various timeframes. Many successful traders on our platform participate in daily, weekly, and monthly challenges concurrently to maximize their earning potential and diversify their approach. Your dashboard will clearly display all active challenges with their respective performance metrics and time remaining.",
    category: "Trading Challenges"
  },
  {
    id: "faq-7",
    question: "How does the dynamic prize pool work?",
    answer: "Our dynamic prize pool grows with each participant and distributes funds transparently:\n\n• 75% of all entry fees go directly back to the community\n   - 60% allocated to top performers (1st, 2nd, and 3rd place)\n   - 15% converted to wallet credits for the top 30% of traders\n\n• The remaining 25% supports platform operations\n\nExample: Daily challenge with 100 participants at $20 each\n• Total pool: $2,000\n• Top performers: $1,200\n• Wallet credits: $300\n• Platform operations: $500\n\nThis creates a truly community-driven reward system where everyone benefits from platform growth.",
    category: "Prizes & Rewards"
  },
  {
    id: "faq-8",
    question: "How are prizes distributed?",
    answer: "Prize distribution varies by challenge type to ensure fair rewards across different timeframes:\n\n• Daily Challenges:\n   - 1st place: 50% of prize pool\n   - 2nd place: 30% of prize pool\n   - 3rd place: 20% of prize pool\n\n• Weekly Challenges:\n   - 1st place: 40% of prize pool\n   - 2nd place: 25% of prize pool\n   - 3rd place: 15% of prize pool\n   - 4th-10th places: 20% split proportionally\n\n• Monthly Challenges:\n   - 1st place: 30% of prize pool\n   - 2nd place: 20% of prize pool\n   - 3rd place: 15% of prize pool\n   - 4th-15th places: 25% split proportionally\n   - All finishers: 10% as participation credits",
    category: "Prizes & Rewards"
  },
  {
    id: "faq-9",
    question: "What are wallet credits and how do they work?",
    answer: "Wallet credits are our internal currency awarded to the top 30% of performers in each challenge. These credits can be used to enter future challenges (either covering the full entry fee or combined with cryptocurrency for partial payments). Credits are automatically added to your account at the end of each challenge based on your performance. They expire after 30 days of account inactivity to encourage regular participation. For example, if you finish in the top 30% of a challenge with 100 participants and a $2,000 prize pool, you might receive $10-15 in wallet credits depending on your exact placement.",
    category: "Prizes & Rewards"
  },
  {
    id: "faq-10",
    question: "When and how do I receive my winnings?",
    answer: "Winners receive cryptocurrency payouts within 24 hours of challenge completion. Once a challenge ends, our system automatically calculates final rankings and prize distributions. After a brief admin review to verify compliance with trading requirements, payouts are processed to the cryptocurrency wallet address in your account. You'll receive an email and Discord notification when your payment is initiated, along with a transaction ID for tracking. There are no minimum withdrawal amounts, hidden fees, or complex verification processes - just straightforward, prompt payments for your trading success.",
    category: "Prizes & Rewards"
  },
  {
    id: "faq-11",
    question: "How do I connect my cTrader account?",
    answer: "Connecting your cTrader account is a simple, secure process: After registering and paying for a challenge, you'll be redirected to cTrader's official OAuth page. Log in with your existing cTrader credentials and authorize our application with the requested permissions. This creates a secure connection that allows us to read your trading data without accessing your funds or account credentials. We store only your access token (not your password) and automatically sync your trading data in real-time throughout the challenge period. You can revoke this access at any time through your cTrader account settings.",
    category: "Technical"
  },
  {
    id: "faq-12",
    question: "What happens if there's an issue with my cTrader connection?",
    answer: "We've built a robust system to handle connection issues: If we detect a problem with your cTrader connection, our system will automatically attempt to refresh your access token up to 3 times. If problems persist, you'll receive immediate notifications via email and Discord with troubleshooting steps. Our platform includes automatic retry mechanisms that run every 30 minutes until the connection is restored. In rare cases where technical issues are on cTrader's side, our support team will work directly with you to ensure your challenge participation isn't unfairly affected, potentially extending your challenge timeframe if necessary.",
    category: "Technical"
  },
  {
    id: "faq-13",
    question: "How often is the leaderboard updated and how accurate is it?",
    answer: "Our leaderboard updates in real-time whenever a trader closes a position, providing immediate tracking of your standing against other participants. Each update pulls comprehensive trading metrics including profit/loss, drawdown percentages, number of trades, and risk management statistics. All data comes directly from cTrader's official API, ensuring complete accuracy and preventing any manipulation. The leaderboard displays both absolute values and relative percentages to give you a clear picture of your performance.",
    category: "Technical"
  },
  {
    id: "faq-14",
    question: "What happens if I exceed the maximum drawdown limit?",
    answer: "If you exceed the maximum drawdown limit for your challenge type (4% for daily challenges, 6% for weekly challenges, or 10% for monthly challenges), you'll be automatically disqualified from prize contention. Our system calculates drawdown based on your account equity relative to your starting balance, not just closed trades. Upon disqualification, you'll receive an immediate notification via email and Discord, and your status on the leaderboard will be updated to reflect this. Unlike prop firms, we don't use hidden rules or manipulate market conditions to force disqualifications - our limits are transparent and applied equally to all participants.",
    category: "Rules & Requirements"
  },
  {
    id: "faq-15",
    question: "Are there any trading restrictions during challenges?",
    answer: "We maintain minimal trading restrictions to create a fair, realistic environment. You can trade all major forex pairs, cryptocurrencies, indices, and commodities available on cTrader. There are no specific session restrictions, no manipulated spreads or slippage, and no arbitrary limitations on strategy types. The only restrictions are: 1) No use of prohibited expert advisors that exploit latency or platform vulnerabilities, 2) No coordinated trading between multiple accounts, and 3) Compliance with the minimum trading requirements for your challenge type. Otherwise, you're free to trade exactly as you would in your personal account.",
    category: "Rules & Requirements"
  },
  {
    id: "faq-16",
    question: "What are the minimum trading requirements for each challenge type?",
    answer: "Each challenge type has specific minimum trading requirements designed to ensure fair competition:\n\n• Daily Challenges\n   - At least 2 separate trades (entries and exits)\n   - Prevents lucky single-trade wins\n\n• Weekly Challenges\n   - Minimum of 3 trades\n   - Must trade on at least 2 different days\n   - Demonstrates consistency\n\n• Monthly Challenges (either option)\n   - Option 1: Trade on 6 different days throughout the month\n   - Option 2: Complete 3 swing trades held for at least 2 days each\n   - Accommodates both active and position trading styles\n\nThese requirements prevent gaming the system while still allowing for different legitimate trading approaches.",
    category: "Rules & Requirements"
  },
  {
    id: "faq-17",
    question: "What payment methods do you accept and how secure are they?",
    answer: "We accept cryptocurrency payments through NOWPayments, a secure, established payment processor. Supported cryptocurrencies include Bitcoin (BTC), Ethereum (ETH), Tether (USDT), USD Coin (USDC), Binance Coin (BNB), and several other popular options. The payment process is fully encrypted and typically confirms within minutes, allowing you to start trading immediately after connecting your cTrader account. We don't store your payment information, and all transactions are processed through secure channels with industry-standard encryption. For added security, we provide unique payment addresses for each transaction.",
    category: "Payments & Accounts"
  },
  {
    id: "faq-18",
    question: "Can I use wallet credits for partial payments and how does it work?",
    answer: "Yes, you can combine wallet credits with cryptocurrency payments when entering challenges. During checkout, our system automatically detects your available credit balance and gives you the option to apply it toward your entry fee. You can choose to use all available credits or a specific amount. The system will then calculate the remaining balance to be paid in cryptocurrency. For example, if you have $15 in wallet credits and want to enter a $50 challenge, you can apply your credits and pay only $35 in cryptocurrency. This flexible system makes it easier to participate in multiple challenges and rewards consistent participation on our platform.",
    category: "Payments & Accounts"
  },
  {
    id: "faq-19",
    question: "How do I check my wallet balance and transaction history?",
    answer: "Your wallet balance and complete transaction history are easily accessible from your account dashboard. After logging in, navigate to the 'Wallet' section to view your current balance, pending credits, and a detailed breakdown of all transactions. The transaction history includes entry fee payments, challenge winnings, credit awards, and credit usage, each with timestamps and associated challenge IDs. The system also displays any credits that will expire soon, with countdown timers for each. You can download your complete transaction history as a CSV file for record-keeping or tax purposes at any time.",
    category: "Payments & Accounts"
  },
  {
    id: "faq-20",
    question: "How do I connect my Discord account and what are the benefits?",
    answer: "Connecting your Discord account enhances your TradeChampionX experience: After registering, go to your account settings and select 'Connect Discord'. You'll be redirected to Discord's authorization page where you can approve the connection. Once linked, you'll automatically receive the 'Verified Challenger' role in our Discord community, unlocking access to exclusive channels for challenge discussions, trading tips, and special announcements. Connected accounts also receive automated notifications about challenge status, leaderboard updates, and payouts directly through Discord. This integration ensures you never miss important information about your challenges.",
    category: "Community"
  },
  {
    id: "faq-21",
    question: "What benefits does the Discord community offer to traders?",
    answer: "Our Discord community provides substantial value beyond just the challenges: You'll get real-time updates on challenge progress and leaderboard changes, participate in focused trading discussions with fellow challengers, access educational content from successful traders, receive direct support from our team, and get early announcements about new features and special events. Active community members also benefit from networking opportunities, strategy sharing sessions, market analysis discussions, and special perks like reduced entry fees for certain challenges. The community creates an environment where traders can learn from each other while competing.",
    category: "Community"
  },
  {
    id: "faq-22",
    question: "What types of community events do you host?",
    answer: "We regularly host diverse community events to enhance the trading experience: Monthly webinars featuring successful traders from our platform who share their strategies and insights, special themed challenges with unique rules and enhanced prize pools, trading competitions with alternative scoring methods beyond just P&L, educational workshops on specific trading techniques or market analysis, and community feedback sessions where members can directly influence platform development. These events are announced in advance through Discord and email, and many include recordings for those who can't attend live. Most events are free for all registered users.",
    category: "Community"
  }
];

export default FAQSection;
