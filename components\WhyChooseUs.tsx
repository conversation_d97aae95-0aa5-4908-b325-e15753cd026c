import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Award, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Clock, Users } from "lucide-react";

const features = [
  {
    title: "Transparent Rules",
    description: "Unlike prop firms with hidden clauses designed to disqualify traders, our rules are simple, clear, and fair. We want you to succeed.",
    icon: <PERSON><PERSON><PERSON><PERSON><PERSON>,
    color: "text-forex-primary",
    bgColor: "bg-forex-primary/10"
  },
  {
    title: "Real Withdrawals",
    description: "No withdrawal delays or impossible hurdles. When you win, you get paid. Period. Your success is our priority.",
    icon: HeartHandshake,
    color: "text-forex-secondary",
    bgColor: "bg-forex-secondary/10"
  },
  {
    title: "No Shady Tactics",
    description: "Tired of price manipulation, slippage, and widened spreads? Our platform uses direct cTrader API connections for fairness.",
    icon: AlertCircle,
    color: "text-forex-accent",
    bgColor: "bg-forex-accent/10"
  },
  {
    title: "Community Rewards",
    description: "75% of entry fees go directly to top performers. The bigger our community grows, the bigger your potential rewards.",
    icon: Award,
    color: "text-forex-profit",
    bgColor: "bg-forex-profit/10"
  }
];

const comparisonPoints = [
  {
    propFirm: "Long Evaluations (30-60 days)",
    tradechampionx: "Instant Daily/Weekly Challenges",
    highlight: true
  },
  {
    propFirm: "Delayed Payouts (2-3 months)",
    tradechampionx: "Next-Day Payouts",
    highlight: false
  },
  {
    propFirm: "Focus on Capital",
    tradechampionx: "Focus on Skill",
    highlight: true
  },
  {
    propFirm: "Hidden Fees & Subscriptions",
    tradechampionx: "Transparent One-Time Payments",
    highlight: false
  },
  {
    propFirm: "Profit When You Fail",
    tradechampionx: "75% to Community Pool",
    highlight: true
  }
];

const WhyChooseUs = () => {
  return (
    <section className="pt-12 pb-20 bg-gradient-to-br from-[#0f1c2e] via-[#1a2c4c] to-[#0f1c2e]">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-3xl mx-auto mb-8">
          <div className="relative inline-block mb-2">
            <div className="h-0.5 w-24 bg-gradient-to-r from-transparent via-blue-400 to-transparent mx-auto mb-6"></div>
            <span className="inline-block px-6 py-2 bg-[#0f1c2e] text-blue-400 font-semibold rounded-lg mb-4 border border-blue-500/50 shadow-md backdrop-blur-sm">
              WHY CHOOSE TRADECHAMPIONX
            </span>
          </div>

          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
            Built By Traders, For Traders
          </h2>

          <p className="text-lg text-blue-100/90 max-w-2xl mx-auto mb-4">
            We've experienced the frustrations of typical prop firms and created something better
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
          {features.map((feature, index) => (
            <div
              key={index}
              className="relative bg-gradient-to-br from-[#1a2c4c]/90 to-[#0f1c2e]/70 backdrop-blur-sm border border-blue-400/10 rounded-xl p-6 overflow-hidden group"
              data-aos="fade-up"
              data-aos-delay={index * 50}
            >
              {/* Animated background effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              {/* Animated border glow on hover */}
              <div className="absolute inset-0 rounded-xl border border-blue-500/0 group-hover:border-blue-500/30 transition-all duration-500 group-hover:shadow-md"></div>

              {/* Icon with animated background */}
              <div className="relative">
                <div className={`w-14 h-14 rounded-lg ${feature.bgColor} flex items-center justify-center mb-5 transform group-hover:scale-110 transition-all duration-500 group-hover:shadow-md`}>
                  <feature.icon className={`w-7 h-7 ${feature.color} group-hover:animate-pulse`} />
                </div>

                {/* Subtle glow effect instead of particles */}
                <div className="absolute -top-1 -right-1 w-10 h-10 rounded-full bg-blue-500/20 blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute -bottom-1 -left-1 w-8 h-8 rounded-full bg-indigo-500/20 blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>

              {/* Title with gradient on hover */}
              <h3 className="text-2xl font-bold mb-3 text-white group-hover:bg-gradient-to-r group-hover:from-white group-hover:to-blue-400 group-hover:bg-clip-text group-hover:text-transparent transition-all duration-500">
                {feature.title}
              </h3>

              {/* Description with increased opacity on hover */}
              <p className="text-blue-100/70 group-hover:text-blue-100/90 transition-colors duration-500">
                {feature.description}
              </p>

              {/* Subtle bottom border indicator on hover */}
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500/0 via-blue-500/50 to-blue-500/0 opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
            </div>
          ))}
        </div>

        {/* Revenue Distribution Comparison */}
        <div className="mt-16 max-w-3xl mx-auto">
          <div className="text-center mb-8">
            <span className="inline-block px-4 py-1.5 bg-blue-500/20 text-blue-400 font-medium rounded-full mb-4 border border-blue-500/40 shadow-sm">
              Community-First Model
            </span>
            <h3 className="text-2xl md:text-3xl font-bold text-blue-100 mb-4">
              Where Your Money Goes
            </h3>
            <p className="text-blue-100/80">
              Unlike prop firms that profit when you fail, we share 75% of all entry fees with our community
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* TradeChampionX Model */}
            <div className="bg-[#1a2c4c]/80 backdrop-blur-sm border border-blue-400/10 rounded-xl p-6 overflow-hidden relative transform transition-transform hover:scale-105">
              <div className="absolute top-0 right-0 bg-blue-500 text-white px-3 py-1 rounded-bl-lg text-sm font-medium">
                Our Model
              </div>
              <h4 className="text-xl font-bold text-blue-100 mb-4">TradeChampionX</h4>

              <div className="flex items-center justify-center mb-6">
                <div className="relative w-40 h-40">
                  <div className="absolute inset-0 rounded-full border-8 border-blue-500/30"></div>
                  <div className="absolute inset-0 rounded-full border-8 border-transparent border-t-blue-500 animate-spin-slow"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div>
                      <div className="text-3xl font-bold text-blue-400">75%</div>
                      <div className="text-sm text-blue-100/70">To Community</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-blue-100/70">Top Performers</span>
                  <span className="text-blue-400 font-medium">60%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-blue-100/70">Wallet Credits (Top 30%)</span>
                  <span className="text-blue-400 font-medium">15%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-blue-100/70">Platform Operations</span>
                  <span className="text-blue-100/70 font-medium">25%</span>
                </div>
              </div>
            </div>

            {/* Traditional Prop Firm Model */}
            <div className="bg-[#1a2c4c]/80 backdrop-blur-sm border border-blue-400/10 rounded-xl p-6 overflow-hidden relative transform transition-transform hover:scale-105">
              <div className="absolute top-0 right-0 bg-red-500 text-white px-3 py-1 rounded-bl-lg text-sm font-medium">
                Their Model
              </div>
              <h4 className="text-xl font-bold text-blue-100 mb-4">Traditional Prop Firms</h4>

              <div className="flex items-center justify-center mb-6">
                <div className="relative w-40 h-40">
                  <div className="absolute inset-0 rounded-full border-8 border-red-500/30"></div>
                  <div className="absolute inset-0 rounded-full border-8 border-transparent border-t-red-500 animate-spin-slow"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div>
                      <div className="text-3xl font-bold text-red-400">80%+</div>
                      <div className="text-sm text-blue-100/70">To Company</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-blue-100/70">Profit from Failures</span>
                  <span className="text-red-400 font-medium">High</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-blue-100/70">Disqualification Rate</span>
                  <span className="text-red-400 font-medium">70-90%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-blue-100/70">To Traders</span>
                  <span className="text-blue-100/70 font-medium">~20%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-16 max-w-3xl mx-auto">
          <div className="text-center mb-8">
            <span className="inline-block px-4 py-1.5 bg-gradient-to-r from-blue-500/20 to-indigo-500/20 text-blue-400 font-medium rounded-full mb-3 border border-blue-500/40 shadow-sm">
              The TradeChampionX Difference
            </span>
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-3 bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
              How We Compare
            </h3>
          </div>

          {/* Compact, Modern Comparison with Wow Factor */}
          <div className="relative bg-[#1a2c4c]/60 backdrop-blur-sm border border-blue-400/10 rounded-xl overflow-hidden">
            {/* Animated background glow */}
            <div className="absolute -top-20 -right-20 w-40 h-40 bg-blue-500/10 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-indigo-500/10 rounded-full blur-3xl"></div>

            {/* Header */}
            <div className="grid grid-cols-3 border-b border-blue-400/10">
              <div className="p-3 text-center"></div>
              <div className="p-3 text-center border-x border-blue-400/10 bg-red-500/5">
                <span className="text-red-400 font-medium text-sm">Traditional Firms</span>
              </div>
              <div className="p-3 text-center bg-blue-500/5">
                <span className="text-blue-400 font-medium text-sm">TradeChampionX</span>
              </div>
            </div>

            {/* Comparison rows with hover effects */}
            {comparisonPoints.map((point, index) => (
              <div key={index} className={`grid grid-cols-3 border-b border-blue-400/10 group hover:bg-blue-500/5 transition-colors duration-300 ${index === comparisonPoints.length - 1 ? 'border-b-0' : ''}`}>
                <div className="p-3 flex items-center">
                  <div className={`w-1 h-8 ${point.highlight ? 'bg-blue-500' : 'bg-blue-500/30'} rounded-full mr-2`}></div>
                  <span className="text-white font-medium text-sm">{point.propFirm.split('(')[0]}</span>
                </div>
                <div className="p-3 text-center border-x border-blue-400/10 bg-red-500/5 group-hover:bg-red-500/10 transition-colors duration-300">
                  <div className="flex items-center justify-center space-x-1">
                    <span className="text-red-400 text-sm">{point.propFirm}</span>
                    <span className="text-red-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300">✗</span>
                  </div>
                </div>
                <div className="p-3 text-center bg-blue-500/5 group-hover:bg-blue-500/10 transition-colors duration-300">
                  <div className="flex items-center justify-center space-x-1">
                    <span className="text-blue-400 font-medium text-sm">{point.tradechampionx}</span>
                    <span className="text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300">✓</span>
                  </div>
                </div>
              </div>
            ))}

            {/* Animated highlight bar that follows mouse on hover (visual flair) */}
            <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500/0 via-blue-500/50 to-blue-500/0 opacity-0 group-hover:opacity-100"></div>
          </div>

          <div className="mt-6 flex justify-center">
            <div className="inline-flex items-center px-4 py-2 bg-[#1a2c4c]/80 backdrop-blur-sm border border-blue-500/20 rounded-lg shadow-sm hover:shadow-blue-500/10 transition-all">
              <span className="text-blue-100 font-bold text-sm bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">#NoMoreShadyPropFirms</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
